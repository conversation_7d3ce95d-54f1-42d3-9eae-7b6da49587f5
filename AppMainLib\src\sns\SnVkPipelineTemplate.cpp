#include "SnVkPipelineTemplate.h"
#include "../../UaIrrlicht/include/ISceneManager.h"
#include "../../UaIrrlicht/include/IVideoDriver.h"
#include "../../UaIrrlicht/include/IMaterialRendererServices.h"

namespace irr
{
namespace scene
{

// Static member initialization
video::MrSnTemplate* SnVkPipelineTemplate::s_materialRenderer = nullptr;
video::E_MATERIAL_TYPE SnVkPipelineTemplate::s_materialType = video::EMT_SOLID;
bool SnVkPipelineTemplate::s_initialized = false;

SnVkPipelineTemplate::SnVkPipelineTemplate(ISceneNode* parent, ISceneManager* mgr, s32 id,
                                          const SnVkPipelineTemplateParam& params)
    : ISceneNode(parent, mgr, id)
    , m_params(params)
    , m_geometryDirty(true)
    , m_materialRenderer(nullptr)
    , m_animationTime(0.0f)
    , m_animationSpeed(1.0f)
    , m_waveAmplitude(0.1f)
    , m_waveFrequency(1.0f)
    , m_lastTimeMs(0)
    , m_enableLighting(params.enableLighting)
    , m_enableCompute(params.enableCompute)
    , m_lightDirection(0.0f, -1.0f, 0.0f)
    , m_lightColor(255, 255, 255, 255)
    , m_materialColor(params.materialColor)
{
    // Set initial transform
    setPosition(params.initialPosition);
    setScale(params.initialScale);

    // Reserve space for geometry
    m_vertices.reallocate(params.maxVertices);
    m_indices.reallocate(params.maxIndices);

    // Initialize material
    m_material.MaterialType = getMaterialType();
    m_material.Lighting = m_enableLighting;
    m_material.BackfaceCulling = true;
    m_material.FrontfaceCulling = false;

    // Set material color
    m_material.DiffuseColor = m_materialColor;
    m_material.AmbientColor = m_materialColor;
    m_material.SpecularColor = video::SColor(255, 128, 128, 128);
    m_material.Shininess = 32.0f;

    // Create default geometry (a simple quad)
    createPlane(core::vector2df(1.0f, 1.0f));

    // Update bounding box
    updateBoundingBox();
}

SnVkPipelineTemplate::~SnVkPipelineTemplate()
{
    // Cleanup is handled by the material renderer
}

void SnVkPipelineTemplate::OnRegisterSceneNode()
{
    if (IsVisible) {
        SceneManager->registerNodeForRendering(this);
    }

    ISceneNode::OnRegisterSceneNode();
}

void SnVkPipelineTemplate::render()
{
    if (!IsVisible || m_vertices.size() == 0 || m_indices.size() == 0) {
        return;
    }

    video::IVideoDriver* driver = SceneManager->getVideoDriver();
    if (!driver) {
        return;
    }

    // Set transformation matrix
    driver->setTransform(video::ETS_WORLD, getAbsoluteTransformation());

    // Update material uniforms
    updateMaterialUniforms();

    // Set material
    driver->setMaterial(m_material);

    // Update geometry if needed
    if (m_geometryDirty) {
        updateGeometry();
        m_geometryDirty = false;
    }

    // Render geometry
    if (m_vertices.size() > 0 && m_indices.size() > 0) {
        driver->drawIndexedTriangleList(
            m_vertices.pointer(), m_vertices.size(),
            m_indices.pointer(), m_indices.size() / 3
        );
    }

    // Dispatch compute shader if enabled
    if (m_enableCompute && s_materialRenderer) {
        s_materialRenderer->dispatchCompute(m_computeInputData.size());
    }
}

void SnVkPipelineTemplate::OnAnimate(u32 timeMs)
{
    if (m_lastTimeMs == 0) {
        m_lastTimeMs = timeMs;
    }

    f32 deltaTime = (timeMs - m_lastTimeMs) / 1000.0f;
    m_animationTime += deltaTime * m_animationSpeed;
    m_lastTimeMs = timeMs;

    // Update material renderer time
    if (s_materialRenderer) {
        s_materialRenderer->setTime(m_animationTime);
        s_materialRenderer->setDeltaTime(deltaTime);
    }

    // Animate vertices if wave effect is enabled
    if (m_waveAmplitude > 0.0f && m_waveFrequency > 0.0f) {
        for (u32 i = 0; i < m_vertices.size(); ++i) {
            video::S3DVertex& vertex = m_vertices[i];
            f32 wave = sin(m_animationTime * m_waveFrequency + vertex.Pos.X + vertex.Pos.Z) * m_waveAmplitude;
            vertex.Pos.Y += wave;
        }
        m_geometryDirty = true;
        updateBoundingBox();
    }

    ISceneNode::OnAnimate(timeMs);
}

const core::aabbox3d<f32>& SnVkPipelineTemplate::getBoundingBox() const
{
    return m_boundingBox;
}

u32 SnVkPipelineTemplate::getMaterialCount() const
{
    return 1;
}

video::SMaterial& SnVkPipelineTemplate::getMaterial(u32 i)
{
    return m_material;
}

void SnVkPipelineTemplate::setMaterialColor(const video::SColor& color)
{
    m_materialColor = color;
    m_material.DiffuseColor = color;
    m_material.AmbientColor = color;

    if (s_materialRenderer) {
        s_materialRenderer->setMaterialColor(color);
    }
}

void SnVkPipelineTemplate::setLightDirection(const core::vector3df& direction)
{
    m_lightDirection = direction;
    m_lightDirection.normalize();

    if (s_materialRenderer) {
        s_materialRenderer->setLightDirection(m_lightDirection);
    }
}

void SnVkPipelineTemplate::setLightColor(const video::SColor& color)
{
    m_lightColor = color;

    if (s_materialRenderer) {
        s_materialRenderer->setLightColor(color);
    }
}

void SnVkPipelineTemplate::clearGeometry()
{
    m_vertices.clear();
    m_indices.clear();
    m_geometryDirty = true;
}

void SnVkPipelineTemplate::addVertex(const video::S3DVertex& vertex)
{
    if (m_vertices.size() < m_params.maxVertices) {
        m_vertices.push_back(vertex);
        m_geometryDirty = true;
    }
}

void SnVkPipelineTemplate::addTriangle(u16 i1, u16 i2, u16 i3)
{
    if (m_indices.size() + 3 <= m_params.maxIndices) {
        m_indices.push_back(i1);
        m_indices.push_back(i2);
        m_indices.push_back(i3);
        m_geometryDirty = true;
    }
}

void SnVkPipelineTemplate::addQuad(u16 i1, u16 i2, u16 i3, u16 i4)
{
    addTriangle(i1, i2, i3);
    addTriangle(i1, i3, i4);
}

void SnVkPipelineTemplate::updateGeometry()
{
    updateBoundingBox();
    // Additional geometry update logic can be added here
}

video::E_MATERIAL_TYPE SnVkPipelineTemplate::getMaterialType()
{
    return s_materialType;
}

void SnVkPipelineTemplate::initializeMaterialRenderer(video::IVideoDriver* driver, io::IFileSystem* fileSystem)
{
    if (!s_initialized && driver && fileSystem) {
        s_materialRenderer = new video::MrSnTemplate(driver, fileSystem);
        s_materialType = driver->addMaterialRenderer(s_materialRenderer);
        video::MrSnTemplate::setMaterialType(s_materialType);
        s_initialized = true;
    }
}

void SnVkPipelineTemplate::updateBoundingBox()
{
    if (m_vertices.size() == 0) {
        m_boundingBox.reset(0, 0, 0);
        return;
    }

    core::vector3df min = m_vertices[0].Pos;
    core::vector3df max = m_vertices[0].Pos;

    for (u32 i = 1; i < m_vertices.size(); ++i) {
        const core::vector3df& pos = m_vertices[i].Pos;

        if (pos.X < min.X) min.X = pos.X;
        if (pos.Y < min.Y) min.Y = pos.Y;
        if (pos.Z < min.Z) min.Z = pos.Z;

        if (pos.X > max.X) max.X = pos.X;
        if (pos.Y > max.Y) max.Y = pos.Y;
        if (pos.Z > max.Z) max.Z = pos.Z;
    }

    m_boundingBox.reset(min, max);
}

void SnVkPipelineTemplate::updateMaterialUniforms()
{
    if (!s_materialRenderer) {
        return;
    }

    // Update material properties
    s_materialRenderer->setMaterialColor(m_materialColor);
    s_materialRenderer->setLightDirection(m_lightDirection);
    s_materialRenderer->setLightColor(m_lightColor);
}

void SnVkPipelineTemplate::createPlane(const core::vector2df& size, u32 segmentsX, u32 segmentsY)
{
    clearGeometry();

    f32 stepX = size.X / segmentsX;
    f32 stepY = size.Y / segmentsY;
    f32 halfX = size.X * 0.5f;
    f32 halfY = size.Y * 0.5f;

    // Create vertices
    for (u32 y = 0; y <= segmentsY; ++y) {
        for (u32 x = 0; x <= segmentsX; ++x) {
            video::S3DVertex vertex;
            vertex.Pos.X = x * stepX - halfX;
            vertex.Pos.Y = 0.0f;
            vertex.Pos.Z = y * stepY - halfY;
            vertex.Normal = core::vector3df(0, 1, 0);
            vertex.TCoords.X = (f32)x / segmentsX;
            vertex.TCoords.Y = (f32)y / segmentsY;
            vertex.Color = video::SColor(255, 255, 255, 255);

            addVertex(vertex);
        }
    }

    // Create indices
    for (u32 y = 0; y < segmentsY; ++y) {
        for (u32 x = 0; x < segmentsX; ++x) {
            u16 i1 = y * (segmentsX + 1) + x;
            u16 i2 = i1 + 1;
            u16 i3 = (y + 1) * (segmentsX + 1) + x;
            u16 i4 = i3 + 1;

            addQuad(i1, i2, i4, i3);
        }
    }

    updateBoundingBox();
}

void SnVkPipelineTemplate::createBox(const core::vector3df& size)
{
    clearGeometry();

    f32 halfX = size.X * 0.5f;
    f32 halfY = size.Y * 0.5f;
    f32 halfZ = size.Z * 0.5f;

    // Define box vertices (8 corners)
    core::vector3df corners[8] = {
        core::vector3df(-halfX, -halfY, -halfZ), // 0
        core::vector3df( halfX, -halfY, -halfZ), // 1
        core::vector3df( halfX,  halfY, -halfZ), // 2
        core::vector3df(-halfX,  halfY, -halfZ), // 3
        core::vector3df(-halfX, -halfY,  halfZ), // 4
        core::vector3df( halfX, -halfY,  halfZ), // 5
        core::vector3df( halfX,  halfY,  halfZ), // 6
        core::vector3df(-halfX,  halfY,  halfZ)  // 7
    };

    // Define face normals
    core::vector3df normals[6] = {
        core::vector3df( 0,  0, -1), // Front
        core::vector3df( 0,  0,  1), // Back
        core::vector3df(-1,  0,  0), // Left
        core::vector3df( 1,  0,  0), // Right
        core::vector3df( 0, -1,  0), // Bottom
        core::vector3df( 0,  1,  0)  // Top
    };

    // Define face vertices (4 vertices per face)
    u16 faces[6][4] = {
        {0, 1, 2, 3}, // Front
        {5, 4, 7, 6}, // Back
        {4, 0, 3, 7}, // Left
        {1, 5, 6, 2}, // Right
        {4, 5, 1, 0}, // Bottom
        {3, 2, 6, 7}  // Top
    };

    // Create vertices for each face
    for (u32 face = 0; face < 6; ++face) {
        for (u32 vertex = 0; vertex < 4; ++vertex) {
            video::S3DVertex v;
            v.Pos = corners[faces[face][vertex]];
            v.Normal = normals[face];
            v.TCoords.X = (vertex == 1 || vertex == 2) ? 1.0f : 0.0f;
            v.TCoords.Y = (vertex == 2 || vertex == 3) ? 1.0f : 0.0f;
            v.Color = video::SColor(255, 255, 255, 255);

            addVertex(v);
        }

        // Add face indices
        u16 baseIndex = face * 4;
        addQuad(baseIndex, baseIndex + 1, baseIndex + 2, baseIndex + 3);
    }

    updateBoundingBox();
}

void SnVkPipelineTemplate::createSphere(f32 radius, u32 segments)
{
    clearGeometry();

    u32 rings = segments / 2;

    // Create vertices
    for (u32 ring = 0; ring <= rings; ++ring) {
        f32 phi = core::PI * ring / rings;
        f32 y = cos(phi) * radius;
        f32 ringRadius = sin(phi) * radius;

        for (u32 segment = 0; segment <= segments; ++segment) {
            f32 theta = 2.0f * core::PI * segment / segments;
            f32 x = cos(theta) * ringRadius;
            f32 z = sin(theta) * ringRadius;

            video::S3DVertex vertex;
            vertex.Pos = core::vector3df(x, y, z);
            vertex.Normal = vertex.Pos;
            vertex.Normal.normalize();
            vertex.TCoords.X = (f32)segment / segments;
            vertex.TCoords.Y = (f32)ring / rings;
            vertex.Color = video::SColor(255, 255, 255, 255);

            addVertex(vertex);
        }
    }

    // Create indices
    for (u32 ring = 0; ring < rings; ++ring) {
        for (u32 segment = 0; segment < segments; ++segment) {
            u16 i1 = ring * (segments + 1) + segment;
            u16 i2 = i1 + 1;
            u16 i3 = (ring + 1) * (segments + 1) + segment;
            u16 i4 = i3 + 1;

            addQuad(i1, i2, i4, i3);
        }
    }

    updateBoundingBox();
}

void SnVkPipelineTemplate::createCylinder(f32 radius, f32 height, u32 segments)
{
    clearGeometry();

    f32 halfHeight = height * 0.5f;

    // Create side vertices
    for (u32 level = 0; level <= 1; ++level) {
        f32 y = level == 0 ? -halfHeight : halfHeight;

        for (u32 segment = 0; segment <= segments; ++segment) {
            f32 theta = 2.0f * core::PI * segment / segments;
            f32 x = cos(theta) * radius;
            f32 z = sin(theta) * radius;

            video::S3DVertex vertex;
            vertex.Pos = core::vector3df(x, y, z);
            vertex.Normal = core::vector3df(x, 0, z);
            vertex.Normal.normalize();
            vertex.TCoords.X = (f32)segment / segments;
            vertex.TCoords.Y = (f32)level;
            vertex.Color = video::SColor(255, 255, 255, 255);

            addVertex(vertex);
        }
    }

    // Create side indices
    for (u32 segment = 0; segment < segments; ++segment) {
        u16 i1 = segment;
        u16 i2 = segment + 1;
        u16 i3 = (segments + 1) + segment;
        u16 i4 = i3 + 1;

        addQuad(i1, i2, i4, i3);
    }

    // Create top and bottom caps
    u16 centerBottom = m_vertices.size();
    u16 centerTop = centerBottom + 1;

    // Add center vertices
    video::S3DVertex centerBottomVertex;
    centerBottomVertex.Pos = core::vector3df(0, -halfHeight, 0);
    centerBottomVertex.Normal = core::vector3df(0, -1, 0);
    centerBottomVertex.TCoords = core::vector2df(0.5f, 0.5f);
    centerBottomVertex.Color = video::SColor(255, 255, 255, 255);
    addVertex(centerBottomVertex);

    video::S3DVertex centerTopVertex;
    centerTopVertex.Pos = core::vector3df(0, halfHeight, 0);
    centerTopVertex.Normal = core::vector3df(0, 1, 0);
    centerTopVertex.TCoords = core::vector2df(0.5f, 0.5f);
    centerTopVertex.Color = video::SColor(255, 255, 255, 255);
    addVertex(centerTopVertex);

    // Add cap triangles
    for (u32 segment = 0; segment < segments; ++segment) {
        // Bottom cap
        addTriangle(centerBottom, segment, segment + 1);

        // Top cap
        addTriangle(centerTop, (segments + 1) + segment + 1, (segments + 1) + segment);
    }

    updateBoundingBox();
}

void SnVkPipelineTemplate::dispatchCompute(u32 elementCount)
{
    if (s_materialRenderer && m_enableCompute) {
        s_materialRenderer->dispatchCompute(elementCount);
    }
}

void SnVkPipelineTemplate::setComputeData(const core::array<core::vector4df>& data)
{
    m_computeInputData = data;
    m_computeOutputData.reallocate(data.size());
    m_computeOutputData.set_used(data.size());
}

core::array<core::vector4df> SnVkPipelineTemplate::getComputeResults()
{
    return m_computeOutputData;
}

} // namespace scene
} // namespace irr

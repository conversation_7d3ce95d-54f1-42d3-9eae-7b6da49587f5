#version 460
precision highp float;
precision highp int;

#extension GL_GOOGLE_include_directive : enable

#include "../FFCodeHeader.glsl"

// Vertex input attributes
layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec3 inNormal;
layout(location = 2) in vec2 inTexCoord;
layout(location = 3) in vec4 inColor;

// Vertex output to fragment shader
layout(location = 0) out vec3 fragWorldPos;
layout(location = 1) out vec3 fragNormal;
layout(location = 2) out vec2 fragTexCoord;
layout(location = 3) out vec4 fragColor;
layout(location = 4) out vec3 fragViewPos;

void main() 
{
    // Transform vertex position to world space
    vec4 worldPos = mWorld * vec4(inPosition, 1.0);
    fragWorldPos = worldPos.xyz;
    
    // Transform to view space
    vec4 viewPos = mView * worldPos;
    fragViewPos = viewPos.xyz;
    
    // Transform to clip space
    gl_Position = mProjection * viewPos;
    
    // Transform normal to world space
    mat3 normalMatrix = mat3(transpose(inverse(mWorld)));
    fragNormal = normalize(normalMatrix * inNormal);
    
    // Pass through texture coordinates and color
    fragTexCoord = inTexCoord;
    fragColor = inColor;
}

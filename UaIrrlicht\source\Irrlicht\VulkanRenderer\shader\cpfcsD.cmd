@echo off
rem THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
rem ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
rem THE IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON>ABILITY AND/OR FITNESS FOR A
rem PARTICULAR PURPOSE.
rem
rem Copyright (c) Microsoft Corporation. All rights reserved.

setlocal

set fxDebug=1
set error=0

set FXCOPTS= -g -V

if %fxDebug% == 1 (
	set FXCOPTS=  -g -V 
)

set PCFXC=glslangValidator

:continue
call :CompileFXDbg mmd frag  
call :CompileFXDbg mmd vert
 call :CompileFX mmd vert 
 call :CompileFX mmd frag 
 call :CompileFXN mmd frag mmdOIT "-DIS_OIT"
call :CompileFX FFCode2D_ProcessImage frag 
 call :CompileFX mmd_trans_vtx vert 
 call :CompileFX mmd_trans_vtx comp 
rem call :CompileFX FFCode3DUI vert 
rem call :CompileFX FFCode3DUI frag
rem call :CompileFXDbg mmd_trans_vtx comp  
rem call :CompileFX mmd_trans_vtx comp 
rem call :CompileFX mmd_trans_vtx vert 
rem call :CompileFXDbg  FwPtcGLSL frag 
rem call :CompileFXDbg FwCsDrawPoints vert 
call :CompileFXDbg FwCsUpdate comp 
call :CompileFXDbg FwCsExpand2v comp 
rem call :CompileFXDbg  FFCodeDF2D frag 
rem call :CompileFXDbg  FwPtcGLSL_oit frag 
 rem  call :CompileFXDbg oit_resolve frag

rem call :CompileFXDbg FFCode2D_CubeToSphere frag 
rem call :CompileFXDbg  FFCode2D_Gradient frag 
rem call :CompileFXDbg FFCubemap vert 
rem call :CompileFXDbg FFCubemap frag 
rem call :CompileFXDbg  FFCode2D_ProcessImage frag 


call :CompileFX snTemplate\SnTemplate comp
call :CompileFX snTemplate\SnTemplate vert
call :CompileFX snTemplate\SnTemplate frag
echo.

if %error% == 0 (
    echo Shaders compiled OK ~~~~~~~~~~~~~~~~~~
) else (
    echo There were shader compilation errors!
)

endlocal
exit /b



:CompileFX
set varName=%1
set varName=%varName:\=__%
set varName=%varName:/=__%
 set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV.h -S %2  %1.%2 -e main  --vn SpirV_%varName%_%2 
echo.
echo %fxc%
%fxc% || set error=1

exit /b

:CompileFXDbg
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV.spv -S %2  %1.%2 -e main 
echo.
echo %fxc%
%fxc% || set error=1
rem spirv-cross Compiled\%1_%2_SpirV.spv --reflect  > Compiled\%1_%2_SpirV.txt
exit /b

:CompileFXN
set fxc=%PCFXC% %FXCOPTS%  -o Compiled\%3_%2_SpirV_base.spv -S %2  %1.%2 -e main  %4   --target-env vulkan1.1
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%3_%2_SpirV_base.spv -o Compiled\%3_%2_SpirV.spv  
call spv2h.exe %3 %2 
exit /b

:needxdk
echo ERROR: CompileShaders xbox requires the Microsoft Xbox One XDK
echo        (try re-running from the XDK Command Prompt)
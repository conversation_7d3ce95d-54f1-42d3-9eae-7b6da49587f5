﻿  VkFixedFunctionMaterialRenderer.cpp
  VkMr2D.cpp
  VkMrFF_MMD.cpp
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMr2D.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkFixedFunctionMaterialRenderer.cpp')
  
D:\AProj\UaIrrlicht\include\matrix4.h(48,11): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_MMD.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkFixedFunctionMaterialRenderer.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(355,44): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(377,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(388,22): warning C4244: '+=': conversion from 'double' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(411,32): warning C4305: '=': truncation from 'double' to 'float'
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file 'VulkanRenderer/VkMrFF_MMD.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkMr2D.cpp(779,44): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(727,39): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244: '=': conversion from 'T' to 'float', possible loss of data
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         with
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         [
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:             T=irr::u32
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(728,40): warning C4244:         ]
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp(935,44): warning C4267: 'return': conversion from 'size_t' to 'irr::u32', possible loss of data
  IrrlichtUP.vcxproj -> D:\AProj\VkUpApp\x64\Release\IrrlichtUP.lib

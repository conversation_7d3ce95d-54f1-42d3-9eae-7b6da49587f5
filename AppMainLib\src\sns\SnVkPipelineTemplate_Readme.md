# Scene Node Template for Vulkan Pipeline

This directory contains a comprehensive template for creating Vulkan-based scene nodes in the UaIrrlicht engine. The template provides a complete framework including shaders, material renderer, and scene node implementation.

## Components Created

### 1. Vulkan Shaders
Located in `shader/`:  (directory linked to `UaIrrlicht/source/Irrlicht/VulkanRenderer/shader/snTemplate/`)
- **`SnTemplate.vert`** - Vertex shader with standard vertex transformations and lighting support
- **`SnTemplate.frag`** - Fragment shader with texture sampling and basic lighting calculations
- **`SnTemplate.comp`** - Compute shader for parallel processing with workgroup operations

### 2. Material Renderer Class
- **`MrSnTemplate.h`** - Header file for the material renderer
- **`MrSnTemplate.cpp`** - Implementation derived from `VkMaterialRenderer`
  - Manages Vulkan resources (buffers, descriptor sets, pipelines)
  - Handles uniform buffer updates
  - Supports both graphics and compute pipelines
  - Provides material property setters (color, lighting, time)

### 3. Scene Node Class
- **`SnVkPipelineTemplate.h`** - Header file for the scene node
- **`SnVkPipelineTemplate.cpp`** - Implementation derived from `ISceneNode`
  - Geometry management (vertices, indices, bounding box)
  - Built-in primitive generation (box, sphere, cylinder, plane)
  - Animation support (wave effects, time-based animations)
  - Compute shader integration
  - Material property management

### 4. Convenience Files
- **`SnTemplate.h`** - Main include file with convenience functions and constants
- **`SnTemplateExample.cpp`** - Usage examples and demonstration code

## Features

### Geometry Generation
- **Box**: Configurable size with proper normals and texture coordinates
- **Sphere**: UV-mapped sphere with configurable segment count
- **Cylinder**: Cylinder with caps, configurable radius and height
- **Plane**: Segmented plane for wave animations
- **Custom**: Manual vertex/index addition for custom shapes

### Animation System
- Time-based animations with configurable speed
- Wave effects with amplitude and frequency control
- Automatic vertex animation for wave effects
- Material property animations

### Lighting Support
- Configurable light direction and color
- Material color and properties
- Specular highlighting with shininess control
- Ambient, diffuse, and specular lighting calculations

### Compute Shader Support
- Input/output buffer management
- Configurable workgroup dispatch
- Data upload/download functionality
- Integration with graphics pipeline

## Quick Start

### 1. Basic Setup
```cpp
#include "SnTemplate.h"

// Initialize once at startup
initializeSnTemplate(driver, fileSystem);

// Create a scene node
TemplateSceneNode* node = createTemplateSceneNode(sceneManager);
node->createBox(vector3df(2, 1, 1));
```

### 2. Custom Configuration
```cpp
SnVkPipelineTemplateParam params;
params.maxVertices = 2000;
params.enableLighting = true;
params.enableCompute = true;
params.materialColor = SColor(255, 128, 255, 128);

TemplateSceneNode* node = createTemplateSceneNode(sceneManager, params);
node->createSphere(1.5f, 32);
node->setAnimationSpeed(2.0f);
```

### 3. Animation and Effects
```cpp
node->setWaveAmplitude(0.2f);
node->setWaveFrequency(1.5f);
node->setLightDirection(vector3df(1, -1, 1));
node->setMaterialColor(SColor(255, 255, 128, 64));
```

### 4. Compute Shader Usage
```cpp
array<vector4df> data;
// Fill data array...
node->setComputeData(data);
node->dispatchCompute(data.size());
// Results available after GPU processing
```

## Architecture

The template follows the established patterns in the UaIrrlicht engine:

1. **Material Renderer** manages Vulkan resources and shader execution
2. **Scene Node** handles geometry, animation, and scene integration
3. **Shaders** implement the actual rendering and compute operations
4. **Static Management** ensures efficient resource sharing between instances

## Customization

### Extending the Material Renderer
- Add new uniform buffer structures
- Implement additional shader stages
- Add custom descriptor set layouts
- Extend compute shader functionality

### Extending the Scene Node
- Add new geometry generation methods
- Implement custom animation systems
- Add physics integration
- Extend material property management

### Shader Modifications
- Add new vertex attributes
- Implement advanced lighting models
- Add post-processing effects
- Extend compute shader algorithms

## Reference

Based on the `SnCsParticle` implementation with `FwCs*.*` shaders as reference, following the established patterns and conventions of the UaIrrlicht Vulkan renderer.
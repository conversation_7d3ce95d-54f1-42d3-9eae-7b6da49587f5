#pragma once

#include "../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkMaterialRenderer.h"
#include "../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkShaderMan/VkFxBase.h"
#include <memory>

namespace irr
{
namespace video
{

// Template-specific uniform buffer structure (matches shader SnTemplateUniforms)
struct SnTemplateUniforms {
    core::matrix4 mWorld;
    core::matrix4 mView;
    core::matrix4 mProjection;
    float4 materialColor;
    float4 lightDirection;
    float4 lightColor;
    f32 time;
    f32 deltaTime;
    core::vector2df padding;
};

// Compute shader parameters (matches shader SnTemplateComputeParams)
struct SnTemplateComputeParams {
    f32 time;
    f32 deltaTime;
    u32 elementCount;
    u32 padding;
};

class MrSnTemplate : public VkMaterialRenderer
{
public:
    MrSnTemplate(IVideoDriver* driver, io::IFileSystem* fileSystem);
    virtual ~MrSnTemplate();

    // Inherited via VkMaterialRenderer
    void InitMaterialRenderer() {};

    // IMaterialRenderer interface
    virtual bool OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId = -1) override;
    virtual void OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                              bool resetAllRenderstates, IMaterialRendererServices* services) override;
    virtual void OnUnsetMaterial() override;
    virtual bool setVariable(const c8* name, const f32* floats, int count) override;
    virtual bool isTransparent() const override { return false; }

    // VkMaterialRenderer interface
    virtual const void* getShaderByteCode() const override;
    virtual u32 getShaderByteCodeSize() const override;
    virtual void cleanFrameCache() override;
    virtual void preSubmit() override;
    virtual void ReloadShaders() override;

    // Template-specific methods
    void setTime(float time) { m_time = time; }
    void setDeltaTime(float deltaTime) { m_deltaTime = deltaTime; }
    void setMaterialColor(const SColor& color);
    void setLightDirection(const core::vector3df& direction);
    void setLightColor(const SColor& color);

    // Compute shader support
    void dispatchCompute(uint32_t elementCount);
    void setComputeInputBuffer(VkBuffer buffer);
    void setComputeOutputBuffer(VkBuffer buffer);

    // Static method to get material type
    static E_MATERIAL_TYPE getMaterialType();
    static void setMaterialType(E_MATERIAL_TYPE type) { s_materialType = type; }

protected:
    void initializeShaders();
    void createUniformBuffers();
    void updateUniformBuffers();
    void setupDescriptorSets();
    void recreatePipeline() override;

private:
    // Shader objects
    std::shared_ptr<VkFxUtil::VkFxBase> m_vertexFragmentShader;
    std::shared_ptr<VkFxUtil::VkFxBase> m_computeShader;

    // Vulkan resources
    VkBuffer m_uniformBuffer;
    VkDeviceMemory m_uniformBufferMemory;
    VkBuffer m_computeUniformBuffer;
    VkDeviceMemory m_computeUniformBufferMemory;

    VkDescriptorSet m_descriptorSet;
    VkDescriptorSet m_computeDescriptorSet;
    VkDescriptorSetLayout m_descriptorSetLayout;
    VkDescriptorSetLayout m_computeDescriptorSetLayout;
    VkPipelineLayout m_pipelineLayout;
    VkPipelineLayout m_computePipelineLayout;
    VkPipeline m_graphicsPipeline;
    VkPipeline m_computePipeline;

    // Uniform data
    SnTemplateUniforms m_uniforms;
    SnTemplateComputeParams m_computeParams;

    // State
    float m_time;
    float m_deltaTime;
    bool m_needsUpdate;

    // Compute buffers
    VkBuffer m_computeInputBuffer;
    VkBuffer m_computeOutputBuffer;

    // Static material type
    static E_MATERIAL_TYPE s_materialType;

    // File system for shader loading
    io::IFileSystem* m_fileSystem;


};

} // namespace video
} // namespace irr

#include "MrSnTemplate.h"
#include "../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkDriver.h"
#include "../../UaIrrlicht/include/IFileSystem.h"

namespace irr
{
namespace video
{

E_MATERIAL_TYPE MrSnTemplate::s_materialType = EMT_SOLID;

MrSnTemplate::MrSnTemplate(IVideoDriver* driver, io::IFileSystem* fileSystem)
    : VkMaterialRenderer(driver)
    , m_fileSystem(fileSystem)
    , m_uniformBuffer(VK_NULL_HANDLE)
    , m_uniformBufferMemory(VK_NULL_HANDLE)
    , m_computeUniformBuffer(VK_NULL_HANDLE)
    , m_computeUniformBufferMemory(VK_NULL_HANDLE)
    , m_descriptorSet(VK_NULL_HANDLE)
    , m_computeDescriptorSet(VK_NULL_HANDLE)
    , m_descriptorSetLayout(VK_NULL_HANDLE)
    , m_computeDescriptorSetLayout(VK_NULL_HANDLE)
    , m_pipelineLayout(VK_NULL_HANDLE)
    , m_computePipelineLayout(VK_NULL_HANDLE)
    , m_graphicsPipeline(VK_NULL_HANDLE)
    , m_computePipeline(VK_NULL_HANDLE)
    , m_time(0.0f)
    , m_deltaTime(0.0f)
    , m_needsUpdate(true)
    , m_computeInputBuffer(VK_NULL_HANDLE)
    , m_computeOutputBuffer(VK_NULL_HANDLE)
{
    if (m_fileSystem)
        m_fileSystem->grab();

    // Initialize uniform data
    memset(&m_uniforms, 0, sizeof(m_uniforms));
    memset(&m_computeParams, 0, sizeof(m_computeParams));

    // Set default values
    m_uniforms.materialColor = float4(1.0f, 1.0f, 1.0f, 1.0f);
    m_uniforms.lightDirection = float4(0.0f, -1.0f, 0.0f, 0.0f);
    m_uniforms.lightColor = float4(1.0f, 1.0f, 1.0f, 1.0f);

    initializeShaders();
    createUniformBuffers();
    setupDescriptorSets();
}

MrSnTemplate::~MrSnTemplate()
{
    // Clean up Vulkan resources
    VkDevice device = Driver->Device;

    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
    }

    if (m_computePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_computePipeline, nullptr);
    }

    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
    }

    if (m_descriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_descriptorSetLayout, nullptr);
    }

    if (m_computeDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_computeDescriptorSetLayout, nullptr);
    }

    if (m_uniformBuffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(device, m_uniformBuffer, nullptr);
    }

    if (m_uniformBufferMemory != VK_NULL_HANDLE) {
        vkFreeMemory(device, m_uniformBufferMemory, nullptr);
    }

    if (m_computeUniformBuffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(device, m_computeUniformBuffer, nullptr);
    }

    if (m_computeUniformBufferMemory != VK_NULL_HANDLE) {
        vkFreeMemory(device, m_computeUniformBufferMemory, nullptr);
    }

    if (m_fileSystem)
        m_fileSystem->drop();
}

bool MrSnTemplate::OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId)
{
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    // Bind descriptor sets and pipeline
    VkCommandBuffer cmdBuffer = Driver->getCurrentCommandBuffer();

    vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_graphicsPipeline);
    vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                           m_pipelineLayout, 0, 1, &m_descriptorSet, 0, nullptr);

    return true;
}

void MrSnTemplate::OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                                bool resetAllRenderstates, IMaterialRendererServices* services)
{
    CurrentMaterial = material;
    services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
    m_needsUpdate = true;
}

void MrSnTemplate::OnUnsetMaterial()
{
    // Nothing to do here for this template
}

bool MrSnTemplate::setVariable(const c8* name, const f32* floats, int count)
{
    // Handle custom shader variables
    if (strcmp(name, "time") == 0 && count >= 1) {
        setTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "deltaTime") == 0 && count >= 1) {
        setDeltaTime(floats[0]);
        return true;
    }
    else if (strcmp(name, "materialColor") == 0 && count >= 4) {
        m_uniforms.materialColor = float4(floats[0], floats[1], floats[2], floats[3]);
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "lightDirection") == 0 && count >= 3) {
        m_uniforms.lightDirection = float4(floats[0], floats[1], floats[2], 0.0f);
        m_needsUpdate = true;
        return true;
    }
    else if (strcmp(name, "lightColor") == 0 && count >= 3) {
        m_uniforms.lightColor = float4(floats[0], floats[1], floats[2], 1.0f);
        m_needsUpdate = true;
        return true;
    }

    return false;
}

const void* MrSnTemplate::getShaderByteCode() const
{
    // Return vertex shader bytecode for pipeline creation
    if (m_vertexFragmentShader) {
        // This would need to be implemented based on your shader loading system
        return nullptr; // Placeholder
    }
    return nullptr;
}

u32 MrSnTemplate::getShaderByteCodeSize() const
{
    // Return vertex shader bytecode size
    return 0; // Placeholder
}

void MrSnTemplate::cleanFrameCache()
{
    // Clean up per-frame resources if any
}

void MrSnTemplate::preSubmit()
{
    // Pre-submission setup if needed
}

void MrSnTemplate::ReloadShaders()
{
    initializeShaders();
    recreatePipeline();
}

void MrSnTemplate::setMaterialColor(const SColor& color)
{
    m_uniforms.materialColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        color.getAlpha() / 255.0f
    );
    m_needsUpdate = true;
}

void MrSnTemplate::setLightDirection(const core::vector3df& direction)
{
    core::vector3df normalized = direction;
    normalized.normalize();
    m_uniforms.lightDirection = float4(normalized.X, normalized.Y, normalized.Z, 0.0f);
    m_needsUpdate = true;
}

void MrSnTemplate::setLightColor(const SColor& color)
{
    m_uniforms.lightColor = float4(
        color.getRed() / 255.0f,
        color.getGreen() / 255.0f,
        color.getBlue() / 255.0f,
        1.0f
    );
    m_needsUpdate = true;
}

E_MATERIAL_TYPE MrSnTemplate::getMaterialType()
{
    return s_materialType;
}

void MrSnTemplate::initializeShaders()
{
    // Load and create vertex/fragment shader
    m_vertexFragmentShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);

    // Load shaders from files (implementation depends on your shader loading system)
    // m_vertexFragmentShader->createVS("SnTemplate.vert", "main");
    // m_vertexFragmentShader->createPS("SnTemplate.frag", "main");

    // Load and create compute shader
    m_computeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
    // m_computeShader->createCS("SnTemplate.comp", "main");
}

void MrSnTemplate::createUniformBuffers()
{
    VkDevice device = Driver->Device;

    // Create uniform buffer for graphics pipeline
    VkBufferCreateInfo bufferInfo{};
    bufferInfo.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
    bufferInfo.size = sizeof(SnTemplateUniforms);
    bufferInfo.usage = VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;
    bufferInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

    vkCreateBuffer(device, &bufferInfo, nullptr, &m_uniformBuffer);

    // Allocate memory for uniform buffer
    VkMemoryRequirements memRequirements;
    vkGetBufferMemoryRequirements(device, m_uniformBuffer, &memRequirements);

    VkMemoryAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    allocInfo.allocationSize = memRequirements.size;
    // allocInfo.memoryTypeIndex = findMemoryType(memRequirements.memoryTypeBits, VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);

    vkAllocateMemory(device, &allocInfo, nullptr, &m_uniformBufferMemory);
    vkBindBufferMemory(device, m_uniformBuffer, m_uniformBufferMemory, 0);

    // Create uniform buffer for compute pipeline
    bufferInfo.size = sizeof(SnTemplateComputeParams);
    vkCreateBuffer(device, &bufferInfo, nullptr, &m_computeUniformBuffer);

    vkGetBufferMemoryRequirements(device, m_computeUniformBuffer, &memRequirements);
    allocInfo.allocationSize = memRequirements.size;

    vkAllocateMemory(device, &allocInfo, nullptr, &m_computeUniformBufferMemory);
    vkBindBufferMemory(device, m_computeUniformBuffer, m_computeUniformBufferMemory, 0);
}

void MrSnTemplate::updateUniformBuffers()
{
    VkDevice device = Driver->Device;

    // Update graphics uniform buffer
    void* data;
    vkMapMemory(device, m_uniformBufferMemory, 0, sizeof(SnTemplateUniforms), 0, &data);
    memcpy(data, &m_uniforms, sizeof(SnTemplateUniforms));
    vkUnmapMemory(device, m_uniformBufferMemory);

    // Update compute uniform buffer
    m_computeParams.time = m_time;
    m_computeParams.deltaTime = m_deltaTime;

    vkMapMemory(device, m_computeUniformBufferMemory, 0, sizeof(SnTemplateComputeParams), 0, &data);
    memcpy(data, &m_computeParams, sizeof(SnTemplateComputeParams));
    vkUnmapMemory(device, m_computeUniformBufferMemory);
}

void MrSnTemplate::setupDescriptorSets()
{
    // Create descriptor set layouts and descriptor sets
    // This is a simplified version - actual implementation would be more complex

    VkDevice device = Driver->Device;

    // Graphics descriptor set layout
    VkDescriptorSetLayoutBinding uboLayoutBinding{};
    uboLayoutBinding.binding = 0;
    uboLayoutBinding.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    uboLayoutBinding.descriptorCount = 1;
    uboLayoutBinding.stageFlags = VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT;

    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = 1;
    layoutInfo.pBindings = &uboLayoutBinding;

    vkCreateDescriptorSetLayout(device, &layoutInfo, nullptr, &m_descriptorSetLayout);

    // Compute descriptor set layout would be created similarly
    // Implementation details depend on your specific descriptor management system
}

void MrSnTemplate::dispatchCompute(uint32_t elementCount)
{
    if (m_computePipeline == VK_NULL_HANDLE) {
        return;
    }

    m_computeParams.elementCount = elementCount;
    m_needsUpdate = true;

    VkCommandBuffer cmdBuffer = Driver->getCurrentCommandBuffer();

    vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_computePipeline);
    vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_computeDescriptorSet, 0, nullptr);

    // Dispatch compute shader
    uint32_t workGroupCount = (elementCount + 63) / 64; // 64 is the local workgroup size
    vkCmdDispatch(cmdBuffer, workGroupCount, 1, 1);
}

void MrSnTemplate::setComputeInputBuffer(VkBuffer buffer)
{
    m_computeInputBuffer = buffer;
    // Update descriptor set to point to new buffer
}

void MrSnTemplate::setComputeOutputBuffer(VkBuffer buffer)
{
    m_computeOutputBuffer = buffer;
    // Update descriptor set to point to new buffer
}

void MrSnTemplate::recreatePipeline()
{
    // Recreate graphics and compute pipelines
    // This would involve creating new VkPipeline objects
    // Implementation depends on your specific Vulkan setup
}

} // namespace video
} // namespace irr

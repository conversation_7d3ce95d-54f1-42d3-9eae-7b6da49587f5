#version 460
precision highp float;
precision highp int;

#extension GL_GOOGLE_include_directive : enable

#include "../FFCodeHeader.glsl"

// Fragment input from vertex shader
layout(location = 0) in vec3 fragWorldPos;
layout(location = 1) in vec3 fragNormal;
layout(location = 2) in vec2 fragTexCoord;
layout(location = 3) in vec4 fragColor;
layout(location = 4) in vec3 fragViewPos;

// Texture samplers
layout(binding = 0) uniform sampler2D texDiffuse;
layout(binding = 1) uniform sampler2D texNormal;

// Fragment output
layout(location = 0) out vec4 outColor;

// Simple lighting calculation
vec3 calculateLighting(vec3 normal, vec3 lightDir, vec3 lightColor, vec3 viewDir)
{
    // Ambient
    vec3 ambient = 0.1 * lightColor;
    
    // Diffuse
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * lightColor;
    
    // Specular
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = spec * lightColor;
    
    return ambient + diffuse + specular;
}

void main() 
{
    // Sample diffuse texture
    vec4 texColor = texture(texDiffuse, fragTexCoord);
    
    // Normalize interpolated normal
    vec3 normal = normalize(fragNormal);
    
    // Simple directional light
    vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
    vec3 lightColor = vec3(1.0, 1.0, 1.0);
    
    // View direction
    vec3 viewDir = normalize(-fragViewPos);
    
    // Calculate lighting
    vec3 lighting = calculateLighting(normal, lightDir, lightColor, viewDir);
    
    // Combine texture color, vertex color, and lighting
    vec3 finalColor = texColor.rgb * fragColor.rgb * lighting;
    
    // Output final color with alpha
    outColor = vec4(finalColor, texColor.a * fragColor.a);
}

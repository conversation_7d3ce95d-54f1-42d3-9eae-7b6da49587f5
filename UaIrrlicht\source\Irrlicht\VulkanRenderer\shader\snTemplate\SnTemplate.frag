#version 460
precision highp float;
precision highp int;

// Uniform buffer for template scene node
layout(binding = 0) uniform SnTemplateUniforms {
    mat4 mWorld;
    mat4 mView;
    mat4 mProjection;
    vec4 materialColor;
    vec4 lightDirection;
    vec4 lightColor;
    float time;
    float deltaTime;
    vec2 padding;
} uniforms;

// Fragment input from vertex shader
layout(location = 0) in vec3 fragWorldPos;
layout(location = 1) in vec3 fragNormal;
layout(location = 2) in vec2 fragTexCoord;
layout(location = 3) in vec4 fragColor;
layout(location = 4) in vec3 fragViewPos;

// Texture samplers
layout(binding = 1) uniform sampler2D texDiffuse;
layout(binding = 2) uniform sampler2D texNormal;

// Fragment output
layout(location = 0) out vec4 outColor;

// Simple lighting calculation
vec3 calculateLighting(vec3 normal, vec3 lightDir, vec3 lightColor, vec3 viewDir)
{
    // Ambient
    vec3 ambient = 0.1 * lightColor;

    // Diffuse
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * lightColor;

    // Specular
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = spec * lightColor;

    return ambient + diffuse + specular;
}

void main()
{
    // Sample diffuse texture
    vec4 texColor = texture(texDiffuse, fragTexCoord);

    // Normalize interpolated normal
    vec3 normal = normalize(fragNormal);

    // Use light direction and color from uniform buffer
    vec3 lightDir = normalize(-uniforms.lightDirection.xyz);
    vec3 lightColor = uniforms.lightColor.rgb;

    // View direction
    vec3 viewDir = normalize(-fragViewPos);

    // Calculate lighting
    vec3 lighting = calculateLighting(normal, lightDir, lightColor, viewDir);

    // Combine texture color, vertex color, material color, and lighting
    vec3 finalColor = texColor.rgb * fragColor.rgb * uniforms.materialColor.rgb * lighting;

    // Output final color with alpha
    float finalAlpha = texColor.a * fragColor.a * uniforms.materialColor.a;
    outColor = vec4(finalColor, finalAlpha);
}

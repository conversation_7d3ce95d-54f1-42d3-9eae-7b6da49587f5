#version 460
precision highp float;
precision highp int;

#extension GL_GOOGLE_include_directive : enable

// Compute shader workgroup size
layout (local_size_x = 64, local_size_y = 1, local_size_z = 1) in;

// Uniform buffer for compute parameters
layout(binding = 0) uniform ComputeParams {
    float time;
    float deltaTime;
    uint elementCount;
    uint padding;
} params;

// Storage buffers for input/output data
layout(binding = 1, std430) restrict readonly buffer InputBuffer {
    vec4 inputData[];
};

layout(binding = 2, std430) restrict writeonly buffer OutputBuffer {
    vec4 outputData[];
};

// Optional shared memory for workgroup operations
shared vec4 sharedData[64];

void main() 
{
    // Get current thread index
    uint index = gl_GlobalInvocationID.x;
    
    // Bounds check
    if (index >= params.elementCount) {
        return;
    }
    
    // Read input data
    vec4 data = inputData[index];
    
    // Example processing: simple transformation based on time
    vec4 result = data;
    result.xyz *= (1.0 + 0.1 * sin(params.time + float(index) * 0.1));
    result.w = data.w; // Preserve alpha/w component
    
    // Optional: Use shared memory for reduction operations
    uint localIndex = gl_LocalInvocationID.x;
    sharedData[localIndex] = result;
    
    // Synchronize workgroup
    barrier();
    
    // Example reduction: sum neighboring elements
    if (localIndex < 32) {
        sharedData[localIndex] += sharedData[localIndex + 32];
    }
    barrier();
    
    if (localIndex < 16) {
        sharedData[localIndex] += sharedData[localIndex + 16];
    }
    barrier();
    
    if (localIndex < 8) {
        sharedData[localIndex] += sharedData[localIndex + 8];
    }
    barrier();
    
    if (localIndex < 4) {
        sharedData[localIndex] += sharedData[localIndex + 4];
    }
    barrier();
    
    if (localIndex < 2) {
        sharedData[localIndex] += sharedData[localIndex + 2];
    }
    barrier();
    
    if (localIndex < 1) {
        sharedData[localIndex] += sharedData[localIndex + 1];
    }
    
    // Write output data
    outputData[index] = result;
    
    // Optional: Write reduction result for first thread in workgroup
    if (localIndex == 0) {
        // Store reduction result somewhere if needed
        // This is just an example - adapt based on your needs
    }
}

#pragma once

#include "../../UaIrrlicht/include/ISceneNode.h"
#include "../../UaIrrlicht/include/SMaterial.h"
#include "../../UaIrrlicht/include/aabbox3d.h"
#include "../../UaIrrlicht/include/S3DVertex.h"
#include "MrSnTemplate.h"

namespace irr
{
namespace scene
{

// Parameters for creating the template scene node
struct SnVkPipelineTemplateParam {
    u32 maxVertices = 1000;
    u32 maxIndices = 3000;
    bool enableCompute = false;
    bool enableLighting = true;
    core::vector3df initialPosition = core::vector3df(0, 0, 0);
    core::vector3df initialScale = core::vector3df(1, 1, 1);
    video::SColor materialColor = video::SColor(255, 255, 255, 255);
};

class SnVkPipelineTemplate : public ISceneNode
{
public:
    SnVkPipelineTemplate(ISceneNode* parent, ISceneManager* mgr, s32 id, 
                        const SnVkPipelineTemplateParam& params = SnVkPipelineTemplateParam());
    virtual ~SnVkPipelineTemplate();

    // ISceneNode interface
    virtual void OnRegisterSceneNode() override;
    virtual void render() override;
    virtual void OnAnimate(u32 timeMs) override;
    virtual const core::aabbox3d<f32>& getBoundingBox() const override;
    virtual u32 getMaterialCount() const override;
    virtual video::SMaterial& getMaterial(u32 i) override;
    virtual ESCENE_NODE_TYPE getType() const override { return ESNT_UNKNOWN; }

    // Template-specific methods
    void setMaterialColor(const video::SColor& color);
    void setLightDirection(const core::vector3df& direction);
    void setLightColor(const video::SColor& color);
    void setEnableCompute(bool enable) { m_enableCompute = enable; }
    void setEnableLighting(bool enable) { m_enableLighting = enable; }

    // Geometry management
    void clearGeometry();
    void addVertex(const video::S3DVertex& vertex);
    void addTriangle(u16 i1, u16 i2, u16 i3);
    void addQuad(u16 i1, u16 i2, u16 i3, u16 i4);
    void updateGeometry();

    // Primitive generation helpers
    void createBox(const core::vector3df& size);
    void createSphere(f32 radius, u32 segments = 16);
    void createPlane(const core::vector2df& size, u32 segmentsX = 1, u32 segmentsY = 1);
    void createCylinder(f32 radius, f32 height, u32 segments = 16);

    // Animation and effects
    void setAnimationSpeed(f32 speed) { m_animationSpeed = speed; }
    void setWaveAmplitude(f32 amplitude) { m_waveAmplitude = amplitude; }
    void setWaveFrequency(f32 frequency) { m_waveFrequency = frequency; }

    // Compute shader support
    void dispatchCompute(u32 elementCount);
    void setComputeData(const core::array<core::vector4df>& data);
    core::array<core::vector4df> getComputeResults();

    // Static material type management
    static video::E_MATERIAL_TYPE getMaterialType();
    static void initializeMaterialRenderer(video::IVideoDriver* driver, io::IFileSystem* fileSystem);

protected:
    void createMesh();
    void updateBoundingBox();
    void updateMaterialUniforms();
    void createBuffers();
    void updateBuffers();

private:
    // Scene node parameters
    SnVkPipelineTemplateParam m_params;
    
    // Geometry data
    core::array<video::S3DVertex> m_vertices;
    core::array<u16> m_indices;
    bool m_geometryDirty;
    
    // Rendering resources
    video::SMaterial m_material;
    core::aabbox3d<f32> m_boundingBox;
    video::MrSnTemplate* m_materialRenderer;
    
    // Animation state
    f32 m_animationTime;
    f32 m_animationSpeed;
    f32 m_waveAmplitude;
    f32 m_waveFrequency;
    u32 m_lastTimeMs;
    
    // Lighting and material properties
    bool m_enableLighting;
    bool m_enableCompute;
    core::vector3df m_lightDirection;
    video::SColor m_lightColor;
    video::SColor m_materialColor;
    
    // Compute shader data
    core::array<core::vector4df> m_computeInputData;
    core::array<core::vector4df> m_computeOutputData;
    
    // Static material renderer instance
    static video::MrSnTemplate* s_materialRenderer;
    static video::E_MATERIAL_TYPE s_materialType;
    static bool s_initialized;
};

} // namespace scene
} // namespace irr

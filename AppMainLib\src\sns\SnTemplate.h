#pragma once

/**
 * Scene Node Template for Vulkan Pipeline
 * 
 * This header provides a complete template for creating Vulkan-based scene nodes
 * in the UaIrrlicht engine. It includes:
 * 
 * 1. Material Renderer (MrSnTemplate) - Handles Vulkan shaders and rendering
 * 2. Scene Node (SnVkPipelineTemplate) - Manages geometry and scene integration
 * 3. Shader support for vertex, fragment, and compute shaders
 * 
 * Usage:
 * 1. Include this header in your project
 * 2. Initialize the material renderer once at startup
 * 3. Create scene node instances as needed
 * 
 * Example:
 * ```cpp
 * #include "SnTemplate.h"
 * 
 * // Initialize once at startup
 * SnVkPipelineTemplate::initializeMaterialRenderer(driver, fileSystem);
 * 
 * // Create scene nodes
 * SnVkPipelineTemplateParam params;
 * params.enableLighting = true;
 * params.materialColor = SColor(255, 128, 255, 128);
 * 
 * SnVkPipelineTemplate* node = new SnVkPipelineTemplate(
 *     sceneManager->getRootSceneNode(), sceneManager, -1, params);
 * 
 * node->createBox(vector3df(2, 1, 1));
 * ```
 */

// Include the main components
#include "MrSnTemplate.h"
#include "SnVkPipelineTemplate.h"

// Convenience namespace alias
namespace SnTemplate = irr::scene;

// Convenience typedefs
typedef irr::scene::SnVkPipelineTemplate TemplateSceneNode;
typedef irr::scene::SnVkPipelineTemplateParam TemplateParams;
typedef irr::video::MrSnTemplate TemplateMaterialRenderer;

/**
 * Convenience function to initialize the template system
 * Call this once at application startup before creating any template scene nodes
 */
inline bool initializeSnTemplate(irr::video::IVideoDriver* driver, irr::io::IFileSystem* fileSystem)
{
    if (!driver || !fileSystem) {
        return false;
    }
    
    irr::scene::SnVkPipelineTemplate::initializeMaterialRenderer(driver, fileSystem);
    return true;
}

/**
 * Convenience function to create a template scene node with default parameters
 */
inline irr::scene::SnVkPipelineTemplate* createTemplateSceneNode(
    irr::scene::ISceneManager* sceneManager,
    irr::scene::ISceneNode* parent = nullptr,
    irr::s32 id = -1)
{
    if (!sceneManager) {
        return nullptr;
    }
    
    if (!parent) {
        parent = sceneManager->getRootSceneNode();
    }
    
    irr::scene::SnVkPipelineTemplateParam params;
    return new irr::scene::SnVkPipelineTemplate(parent, sceneManager, id, params);
}

/**
 * Convenience function to create a template scene node with custom parameters
 */
inline irr::scene::SnVkPipelineTemplate* createTemplateSceneNode(
    irr::scene::ISceneManager* sceneManager,
    const irr::scene::SnVkPipelineTemplateParam& params,
    irr::scene::ISceneNode* parent = nullptr,
    irr::s32 id = -1)
{
    if (!sceneManager) {
        return nullptr;
    }
    
    if (!parent) {
        parent = sceneManager->getRootSceneNode();
    }
    
    return new irr::scene::SnVkPipelineTemplate(parent, sceneManager, id, params);
}

/**
 * Template configuration constants
 */
namespace SnTemplateConfig
{
    // Default geometry limits
    const irr::u32 DEFAULT_MAX_VERTICES = 1000;
    const irr::u32 DEFAULT_MAX_INDICES = 3000;
    
    // Default sphere/cylinder segments
    const irr::u32 DEFAULT_SPHERE_SEGMENTS = 16;
    const irr::u32 DEFAULT_CYLINDER_SEGMENTS = 16;
    
    // Default animation parameters
    const irr::f32 DEFAULT_ANIMATION_SPEED = 1.0f;
    const irr::f32 DEFAULT_WAVE_AMPLITUDE = 0.1f;
    const irr::f32 DEFAULT_WAVE_FREQUENCY = 1.0f;
    
    // Default lighting
    const irr::core::vector3df DEFAULT_LIGHT_DIRECTION = irr::core::vector3df(0.0f, -1.0f, 0.0f);
    const irr::video::SColor DEFAULT_LIGHT_COLOR = irr::video::SColor(255, 255, 255, 255);
    const irr::video::SColor DEFAULT_MATERIAL_COLOR = irr::video::SColor(255, 255, 255, 255);
}

/**
 * Shader file paths (relative to shader directory)
 */
namespace SnTemplateShaders
{
    const char* VERTEX_SHADER = "snTemplate/SnTemplate.vert";
    const char* FRAGMENT_SHADER = "snTemplate/SnTemplate.frag";
    const char* COMPUTE_SHADER = "snTemplate/SnTemplate.comp";
}
